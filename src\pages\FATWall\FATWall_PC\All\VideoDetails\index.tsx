import { FC, useState, useEffect, useMemo, useCallback } from "react";
import styles from "./index.module.scss";
import { LeftOutlined, HeartOutlined, CheckOutlined, MoreOutlined, DownloadOutlined, HeartFilled } from "@ant-design/icons";
import play_white from "@/Resources/player/play_dashboard.png"
import zhixian from "@/Resources/filmWall/zhixian.png"
import { PreloadImage } from "@/components/Image";
import { Modal, message, Popover, Select } from "antd";
import { useHistory, useLocation } from "react-router-dom";
import EpisodeList, { Episode } from "@/components/FATWall_PC/EpisodeList";
import MatchCorrection from "../MatchCorrection";
import { modalShow } from "@/components/List";
import { px2rem } from "@/utils/setRootFontSize";
import { useRequest } from "ahooks";
import {
  mediaDelete,
  move2trashbin,
  getFilePath,
  getMediaDetails,
  getMediaFiles,
  collect,
  markWatched,
  MediaDetailsResponse,
  MediaFileInfo,
  collectEpisode,
  markWatchedEpisode,
} from "@/api/fatWall";
import { playVideo, downloadFiles } from "@/api/fatWallJSBridge";
import CommonUtils from '@/utils/CommonUtils';
import request from '@/request';

interface VideoDetailsProps {
  videoId?: string;
}

// 扩展Episode接口，添加需要的字段
interface ExtendedEpisode extends Episode {
  path?: string;
  file_id?: number;
  resolution?: string;
  hdr?: string;
  audio_codec?: string;
  total_time?: number;
}

// 格式化文件大小的函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const VideoDetails: FC<VideoDetailsProps> = ({ videoId }) => {
  const history = useHistory();
  const location = useLocation();

  // 支持URL参数和state参数两种方式
  const urlParams = new URLSearchParams(location.search);
  const urlClasses = urlParams.get('classes');
  const urlMediaId = urlParams.get('media_id');
  const urlLibId = urlParams.get('lib_id');

  // 优先使用URL参数，如果没有则使用state参数
  const stateParams = location.state as { classes: string, media_id: number, lib_id?: number, isDrama?: boolean } || { classes: '', media_id: 0 };
  const classes = urlClasses || stateParams.classes || '';
  const media_id = urlMediaId ? parseInt(urlMediaId) : (stateParams.media_id || 0);
  const lib_id = urlLibId ? parseInt(urlLibId) : (stateParams.lib_id || 0); // 默认为0表示"All"页面

  const [isLoading, setIsLoading] = useState(true);
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [morePopoverVisible, setMorePopoverVisible] = useState(false);
  const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);
  const [mediaDetails, setMediaDetails] = useState<MediaDetailsResponse | null>(null);
  const [mediaFiles, setMediaFiles] = useState<MediaFileInfo[]>([]);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [isWatched, setIsWatched] = useState<boolean>(false);
  const [episodeThumbnails, setEpisodeThumbnails] = useState<Map<number, string>>(new Map());
  const [currentEpisodeId, setCurrentEpisodeId] = useState<string>("0");
  const [selectedVersionId, setSelectedVersionId] = useState<string>('');
  const [availableVersions, setAvailableVersions] = useState<Array<{ id: string, name: string, path: string }>>([]);
  const [showRefreshToast, setShowRefreshToast] = useState<boolean>(false);
  const [episodeFavorites, setEpisodeFavorites] = useState<Map<number, boolean>>(new Map());
  const [episodeWatched, setEpisodeWatched] = useState<Map<number, boolean>>(new Map());
  const [currentFavoriteOperation, setCurrentFavoriteOperation] = useState<'collect' | 'uncollect' | null>(null);
  const [currentWatchedOperation, setCurrentWatchedOperation] = useState<'watched' | 'unwatched' | null>(null);
  const [hasDownloaded, setHasDownloaded] = useState<boolean>(false); // 是否已经下载过

  // 获取媒体详情
  const { run: runGetMediaDetails } = useRequest(
    getMediaDetails,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          setMediaDetails(res.data);
          // 更新收藏和已看状态
          setIsFavorite(res.data.favourite === 1);
          setIsWatched(res.data.seen === 1);
          setIsLoading(false);

          // 数据刷新成功提示
          if (showRefreshToast) {
            message.success('信息已更新');
            setShowRefreshToast(false);
          }
        }
      },
      onError: () => {
        message.error('获取影视详情失败');
        setIsLoading(false);
      },
    }
  );

  // 获取媒体文件列表
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          setMediaFiles(res.data.files || []);

          // 初始化单集收藏和观看状态
          const favoriteMap = new Map<number, boolean>();
          const watchedMap = new Map<number, boolean>();

          res.data.files.forEach(file => {
            // 使用API返回的实际收藏状态
            favoriteMap.set(file.file_id, file.favourite === 1);
            // 使用API返回的实际观看状态
            watchedMap.set(file.file_id, file.seen === 1);
          });

          setEpisodeFavorites(favoriteMap);
          setEpisodeWatched(watchedMap);
        }
      },
      onError: () => {
        message.error('获取文件列表失败');
      },
    }
  );

  // 收藏/取消收藏
  const { run: runCollect } = useRequest(
    collect,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success(isFavorite ? '已添加到收藏' : '已取消收藏');
        } else if (res.code === 2205) {
          message.error('目标影视项不存在');
          // 恢复状态
          setIsFavorite(prev => !prev);
        } else {
          message.error('操作失败，请重试');
          // 恢复状态
          setIsFavorite(prev => !prev);
        }
      },
      onError: () => {
        message.error('操作失败，请重试');
        // 恢复状态
        setIsFavorite(prev => !prev);
      },
    }
  );

  // 标记已观看/未观看
  const { run: runMarkWatched } = useRequest(
    markWatched,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success(isWatched ? '标记为已观看' : '标记为未观看');
        } else if (res.code === 2205) {
          message.error('目标影视项不存在');
          // 恢复状态
          setIsWatched(prev => !prev);
        } else {
          message.error('操作失败，请重试');
          // 恢复状态
          setIsWatched(prev => !prev);
        }
      },
      onError: () => {
        message.error('操作失败，请重试');
        // 恢复状态
        setIsWatched(prev => !prev);
      },
    }
  );

  // 单集收藏/取消收藏
  const { run: runCollectEpisode } = useRequest(
    collectEpisode,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          // 根据操作类型显示不同的消息
          const successMessage = currentFavoriteOperation === 'collect' ? '已添加到收藏' : '已取消收藏';
          message.success(successMessage);
          // 成功时不需要重新获取数据，因为本地状态已经更新了
        } else {
          message.error('操作失败，请重试');
          // 失败时恢复状态
          runGetMediaFiles({ lib_id, media_id });
        }
        // 清空操作类型
        setCurrentFavoriteOperation(null);
      },
      onError: () => {
        message.error('操作失败，请重试');
        // 失败时恢复状态
        runGetMediaFiles({ lib_id, media_id });
        // 清空操作类型
        setCurrentFavoriteOperation(null);
      },
    }
  );

  // 单集标记已观看/未观看
  const { run: runMarkWatchedEpisode } = useRequest(
    markWatchedEpisode,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          // 根据操作类型显示不同的消息
          const successMessage = currentWatchedOperation === 'watched' ? '已标记为观看' : '已取消观看';
          message.success(successMessage);
          // 成功时不需要重新获取数据，因为本地状态已经更新了
        } else {
          message.error('操作失败，请重试');
          // 失败时恢复状态
          runGetMediaFiles({ lib_id, media_id });
        }
        // 清空操作类型
        setCurrentWatchedOperation(null);
      },
      onError: () => {
        message.error('操作失败，请重试');
        // 失败时恢复状态
        runGetMediaFiles({ lib_id, media_id });
        // 清空操作类型
        setCurrentWatchedOperation(null);
      },
    }
  );

  const { run: runMediaDelete } = useRequest(
    mediaDelete,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success('已从媒体库移除');
          history.goBack();
        } else {
          message.error('删除失败，请重试');
        }
      },
      onError: () => {
        message.error('删除失败，请重试');
      },
    }
  );

  const { run: runGetFilePath } = useRequest(
    getFilePath,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data && res.data.path) {
          runMove2trashbin({ path: res.data.path });
        } else {
          message.error('获取文件路径失败');
        }
      },
      onError: () => {
        message.error('获取文件路径失败');
      }
    }
  );

  const { run: runMove2trashbin } = useRequest(
    move2trashbin,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success('删除成功');
          history.goBack();
        } else {
          message.error('删除失败，请重试');
        }
      },
      onError: () => {
        message.error('删除失败，请重试');
      },
    }
  );

  // 获取缩略图 - 支持二进制响应，静默更新
  const getThumbnailWithBinary = (params: { path: string, size: string }) => {
    return request.post('/filemgr/get_thumbnail', params, {
      responseType: 'arraybuffer',
      showLoading: false // 静默更新，不显示loading
    });
  };

  // 获取所有集数的缩略图 - 串行版本，加载完一张设置一张
  const fetchAllThumbnails = useCallback(async () => {
    if (mediaFiles && mediaFiles.length > 0) {
      try {
        // 串行获取每个缩略图，避免并发请求导致进程卡死
        for (const file of mediaFiles) {
          try {
            const res = await getThumbnailWithBinary({
              path: file.path,
              size: "medium"
            });

            if (res instanceof ArrayBuffer) {
              // 检查JPEG文件头
              const uint8Array = new Uint8Array(res);
              const isValidJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF;

              let imageUrl: string;
              if (isValidJPEG) {
                const blob = new Blob([res], { type: 'image/jpeg' });
                imageUrl = URL.createObjectURL(blob);
              } else {
                // 尝试作为其他格式
                const blob = new Blob([res], { type: 'image/png' });
                imageUrl = URL.createObjectURL(blob);
              }

              // 加载完一张立即设置一张，提供更好的用户体验
              setEpisodeThumbnails(prev => {
                const newMap = new Map(prev);
                newMap.set(file.file_id, imageUrl);
                return newMap;
              });
            }
          } catch (error) {
            console.error(`获取第${file.file_id}集缩略图失败:`, error);
            // 单个失败不影响其他缩略图的获取，继续处理下一个
          }
        }

      } catch (error) {
        console.error('批量获取缩略图失败:', error);
      }
    }
  }, [mediaFiles]);

  // 清理blob URLs
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有blob URLs
      episodeThumbnails.forEach((url) => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [episodeThumbnails]);

  // 当mediaFiles更新后延迟获取缩略图
  useEffect(() => {
    if (mediaFiles && mediaFiles.length > 0 && classes !== '电影') {
      // 延迟500毫秒开始获取缩略图，等待组件渲染完成
      const timer = setTimeout(() => {
        fetchAllThumbnails();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [mediaFiles, fetchAllThumbnails, classes]);

  useEffect(() => {
    // 调用接口获取媒体详情
    runGetMediaDetails({ media_id });
    // 调用接口获取媒体文件列表
    runGetMediaFiles({ lib_id, media_id });
  }, [runGetMediaDetails, runGetMediaFiles, media_id, lib_id]);

  // 当mediaFiles更新时，生成版本信息（仅电影类型）
  useEffect(() => {
    if (classes === '电影' && mediaFiles && mediaFiles.length >= 2) {
      const versions = mediaFiles.map((file) => ({
        id: file.file_id.toString(),
        name: `${file.resolution} | ${file.hdr} | ${file.audio_codec}`,
        path: file.path
      }));
      setAvailableVersions(versions);
      // 设置默认选择第一个版本
      setSelectedVersionId(versions[0].id);
    } else {
      setAvailableVersions([]);
      setSelectedVersionId('');
    }
  }, [mediaFiles, classes]);

  // 创建一个稳定的缩略图映射对象
  const thumbnailsForFilmList = useMemo(() => {
    const obj: { [key: number]: string } = {};
    episodeThumbnails.forEach((url, fileId) => {
      obj[fileId] = url;
    });
    return obj;
  }, [episodeThumbnails]);

  // 将媒体文件转换为剧集列表格式
  const episodes: ExtendedEpisode[] = useMemo(() => {
    if (!mediaFiles || mediaFiles.length === 0) {
      return [];
    }

    return mediaFiles.map((file) => {
      // 获取该集的缩略图，如果没有则使用默认poster
      const thumbnailUrl = thumbnailsForFilmList[file.file_id];
      const posterUrl = thumbnailUrl || (mediaDetails?.poster && mediaDetails.poster.length > 0 ? mediaDetails.poster[0] : '');

      return {
        id: file.file_id.toString(),
        title: `第${file.episode}集`,
        thumbnail: posterUrl,
        episodeNumber: file.episode,
        watched: episodeWatched.get(file.file_id) ?? false,
        progress: file.last_play_point > 0 ? Math.min(Math.round(file.last_play_point), 100) : 0,
        favorite: episodeFavorites.get(file.file_id) ?? false,
        file_id: file.file_id,
        path: file.path,
        resolution: file.resolution,
        hdr: file.hdr,
        audio_codec: file.audio_codec,
        file_media_id: file?.media_id
      };
    });
  }, [mediaFiles, mediaDetails, thumbnailsForFilmList, episodeFavorites, episodeWatched]);

  const handleBack = () => {
    history.goBack();
  };

  const handlePlay = () => {
    if (!mediaFiles || mediaFiles.length === 0) {
      message.error('暂无可播放的文件');
      return;
    }

    // 构建完整的PC端videoList数组
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: media_id.toString(),
      file_id: file.file_id.toString(),
      total_time: file.duration || 0, // 视频总时长（秒）
      filter: {
        last_play_point: file.last_play_point,
        audio_index: file.audio_index,
        seen: file.seen,
        subtitle_path: file.subtitle_path,
        subtitle_type: file.subtitle_type,
        subtitle_index: file.subtitle_index,
      }
    }));

    let playIndex = 0; // 默认播放第一集

    // 根据classes判断播放逻辑
    if (classes === '电影') {
      // 电影：根据版本选择器确定播放索引
      if (mediaFiles.length >= 2 && selectedVersionId) {
        const selectedFileIndex = mediaFiles.findIndex(file => file.file_id.toString() === selectedVersionId);
        if (selectedFileIndex !== -1) {
          playIndex = selectedFileIndex;
        }
      }
    } else {
      // 剧集类型：根据media_details中的last_seen_file_id在file_list数组中筛选file_id相同的项的索引位置
      if (mediaDetails?.last_seen_file_id) {
        // 遍历mediaFiles数组，找到file_id与last_seen_file_id相同的项的索引
        const targetIndex = mediaFiles.findIndex(file => file.file_id === mediaDetails.last_seen_file_id);
        if (targetIndex !== -1) {
          playIndex = targetIndex;
        }
      }
    }

    // 调用视频播放接口
    playVideo(videoList, playIndex, (res) => {
      if (res.code === 0) {
        message.success('开始播放');
      } else {
        message.error(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      message.error(error.message || '播放失败');
    });
  };

  const handleDownload = () => {
    if (hasDownloaded) {
      return; // 如果已经下载过，直接返回
    }

    if (!mediaFiles || mediaFiles.length === 0) {
      message.error('暂无可下载的文件');
      return;
    }
    // 构造文件信息列表，使用fatWallPlayer接口格式
    const fileList = mediaFiles.map(file => ({
      name: file.path.split('/').pop() || `第${file.episode}集`,
      path: file.path,
      mtime: file.mtime,
      size: file.file_size
    }));

    // 标记为已下载，不管成功与否都不能再次点击
    setHasDownloaded(true);

    // 调用fatWallPlayer封装的下载接口
    downloadFiles(fileList, (res) => {
      if (res.code === 0) {
        message.success('开始下载');
        setDownloadModalVisible(true);
      } else {
        message.error(`下载失败: ${res.msg}`);
      }
    }).catch((error) => {
      message.error(error.message || '下载失败');
    });
  };

  const handleCloseDownloadModal = () => {
    setDownloadModalVisible(false);
  };

  const handleToggleFavorite = () => {
    const newFavoriteStatus = !isFavorite;
    setIsFavorite(newFavoriteStatus);

    // 调用收藏API
    runCollect({
      media_ids: [media_id],
      favourite: newFavoriteStatus ? 1 : 0
    });
  };

  const handleToggleWatched = () => {
    const newWatchedStatus = !isWatched;
    setIsWatched(newWatchedStatus);

    // 调用标记已观看API
    runMarkWatched({
      media_ids: [media_id],
      seen: newWatchedStatus ? 1 : 0
    });
  };

  const toggleDescription = useCallback(() => {
    setShowFullDescription(prev => !prev);
  }, []);

  const handleMoreMenuClick = () => {
    setMorePopoverVisible(!morePopoverVisible);
  };

  // 添加刷新函数
  const refreshData = useCallback((newMediaId?: number) => {
    // 如果提供了新的media_id，使用它，否则使用当前的media_id
    const mediaIdToUse = newMediaId || media_id;

    // 刷新媒体详情和文件列表
    runGetMediaDetails({ media_id: mediaIdToUse });
    runGetMediaFiles({ lib_id, media_id: mediaIdToUse });

    // 如果提供了新的media_id，显示刷新成功提示
    if (newMediaId) {
      setShowRefreshToast(true);
    }
  }, [runGetMediaDetails, runGetMediaFiles, media_id, lib_id]);

  // 修正匹配信息
  const handleEditMatch = () => {
    setMorePopoverVisible(false);
    setShowMatchCorrection(true);
  };

  const move2Trashbin = useCallback((modal) => {
    modal.destroy();
    if (media_id) {
      runMediaDelete({ media_ids: [media_id], lib_id });
    }
  }, [media_id, lib_id, runMediaDelete]);

  const delFile = useCallback((modal) => {
    modalShow(`是否确定删除文件？`, <>删除的文件将移至"回收站"，保留30天</>, (m => {
      m.destroy();
      modal.destroy();

      // 先获取文件路径，然后再移动到回收站
      if (media_id) {
        runGetFilePath({ media_ids: [media_id], lib_id });
      }
    }), () => null, false, { position: 'center', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } });
  }, [media_id, lib_id, runGetFilePath]);

  // 删除
  const handleDelete = useCallback(() => {
    setMorePopoverVisible(false);

    const m = modalShow('确认删除吗？', (
      <>
        <div className={styles.modal_button} onClick={() => move2Trashbin(m)}>仅从媒体库移除</div>
        <div className={styles.modal_button} style={{ color: 'var(--emergency-text-color)' }} onClick={() => delFile(m)}>删除文件</div>
      </>
    ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'center' });
  }, [delFile, move2Trashbin]);

  const handleToggleEpisodeFavorite = (episode: Episode) => {
    if (!episode.file_id) {
      message.error('无法获取剧集文件ID');
      return;
    }

    const newFavoriteStatus = !episode.favorite;
    const operationType = newFavoriteStatus ? 'collect' : 'uncollect';

    // 保存当前操作类型
    setCurrentFavoriteOperation(operationType);

    // 先更新本地状态，提供即时反馈
    setEpisodeFavorites(prev => {
      const newMap = new Map(prev);
      newMap.set(episode.file_id!, newFavoriteStatus);
      return newMap;
    });

    // 调用API
    runCollectEpisode({
      file_id: episode.file_id,
      favourite: newFavoriteStatus ? 1 : 0
    });
  };

  const handleToggleEpisodeWatched = (episode: Episode) => {
    if (!episode.file_id) {
      message.error('无法获取剧集文件ID');
      return;
    }

    const newWatchedStatus = !episode.watched;
    const operationType = newWatchedStatus ? 'watched' : 'unwatched';

    // 保存当前操作类型
    setCurrentWatchedOperation(operationType);

    // 先更新本地状态，提供即时反馈
    setEpisodeWatched(prev => {
      const newMap = new Map(prev);
      newMap.set(episode.file_id!, newWatchedStatus);
      return newMap;
    });

    // 调用API
    runMarkWatchedEpisode({
      file_id: episode.file_id,
      seen: newWatchedStatus ? 1 : 0
    });
  };

  const handleEpisodeSelect = (episode: Episode) => {
    setCurrentEpisodeId(episode.id);

    if (!mediaFiles || mediaFiles.length === 0) {
      message.error('暂无可播放的文件');
      return;
    }

    // 构建完整的PC端videoList数组
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: media_id.toString(),
      file_id: file.file_id.toString(),
      total_time: file.duration || 0,
      filter: {
        last_play_point: file.last_play_point,
        audio_index: file.audio_index,
        seen: file.seen,
        subtitle_path: file.subtitle_path,
        subtitle_type: file.subtitle_type,
        subtitle_index: file.subtitle_index,
      }
    }));

    // 根据选中的剧集找到对应的索引
    const selectedEpisodeIndex = mediaFiles.findIndex(file => file.file_id.toString() === episode.id);
    const playIndex = selectedEpisodeIndex !== -1 ? selectedEpisodeIndex : 0;

    // 调用视频播放接口
    playVideo(videoList, playIndex, (res) => {
      if (res.code === 0) {
        message.success(`开始播放第${episode.episodeNumber}集`);
      } else {
        message.error(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      message.error(error.message || '播放失败');
    });
  };

  // 处理版本选择
  const handleVersionSelect = (versionId: string) => {
    setSelectedVersionId(versionId);
    const selectedVersion = availableVersions.find(v => v.id === versionId);
    if (selectedVersion) {
      message.success(`已切换到版本: ${selectedVersion.name}`);
    }
  };

  // 计算播放按钮显示文本
  const getPlayButtonText = useMemo(() => {
    // 如果是电视剧类型，显示上次播放的集数
    if (classes === '电视剧') {
      const lastSeenFileId = mediaDetails?.last_seen_file_id;
      if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
        const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
        if (lastSeenFile && lastSeenFile.episode) {
          return `播放 第${lastSeenFile.episode}集`;
        }
      }
      // 没有播放记录时显示第1集
      return mediaFiles && mediaFiles.length > 0 ? '播放 第1集' : '播放';
    } else {
      // 根据 mediaDetails 中的 last_seen_file_id 去 mediaFiles 中找到匹配的文件
      const lastSeenFileId = mediaDetails?.last_seen_file_id;
      if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
        const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
        if (lastSeenFile) {
          const lastPlayPoint = lastSeenFile.last_play_point || 0;
          if (lastPlayPoint > 0) {
            const minutes = Math.floor(lastPlayPoint / 60);
            const seconds = lastPlayPoint % 60;
            return `播放 ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
          }
        }
      }
      return '播放';
    }
  }, [classes, mediaDetails, mediaFiles]);

  // 获取当前选择的版本对应的文件信息
  const selectedVersionFile = useMemo(() => {
    if (classes === '电影' && mediaFiles && mediaFiles.length >= 2) {
      // 电影类型：根据selectedVersionId找到对应的文件
      const selectedFile = mediaFiles.find(file => file.file_id.toString() === selectedVersionId);
      return selectedFile || mediaFiles[0]; // 如果没找到，返回第一个文件
    } else {
      // 非电影类型：返回第一个文件
      return mediaFiles && mediaFiles.length > 0 ? mediaFiles[0] : null;
    }
  }, [classes, mediaFiles, selectedVersionId]);

  // 创建演员列表数据
  const castMembers = useMemo(() => {
    if (!mediaDetails || !mediaDetails.actor_info) return [];

    return mediaDetails.actor_info.map((actor, index) => ({
      id: index.toString(),
      name: actor?.name || '未知演员',
      role: actor.chapter ? `饰 ${actor.chapter}` : '演员',
      avatar: actor.profile_path || ''
    }));
  }, [mediaDetails]);

  // 处理演员点击事件
  const handleActorClick = useCallback((actor: any) => {
    // 跳转到演员详情页，传递演员的profile_path和name
    const params = new URLSearchParams({
      profile_path: actor.avatar || '',
      name: actor.name || '未知演员'
    });

    history.push(`/filmAndTelevisionWall_pc/all/videoDetails/actorDetail?${params.toString()}`);
  }, [history]);

  // 视频信息，使用真实数据
  const videoData = useMemo(() => {
    if (!mediaDetails) {
      return {
        title: '加载中...',
        rating: 0,
        year: 0,
        category: '加载中',
        region: '',
        duration: '0分钟',
        tags: [],
        description: '加载中...',
        filePath: '',
        fileSize: '0 B',
        cast: [],
        playTime: ''
      };
    }

    // 使用接口返回的真实数据
    return {
      title: mediaDetails.trans_name || mediaDetails.origin_name || '未知影片',
      rating: mediaDetails.score || 0,
      year: mediaDetails.year || 0,
      category: mediaDetails.classes || '未知',
      region: mediaDetails.origin_place || '未知',
      duration: (() => {
        const totalMinutes = mediaDetails.video_length;
        if (!totalMinutes) return '未知时长';

        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;

        if (hours === 0) {
          return `${minutes}分钟`;
        } else if (minutes === 0) {
          return `${hours}小时`;
        } else {
          return `${hours}小时${minutes}分钟`;
        }
      })(),
      tags: selectedVersionFile ? [
        selectedVersionFile.resolution,
        selectedVersionFile.hdr,
        selectedVersionFile.audio_codec
      ].filter(Boolean) : [],
      description: mediaDetails.brief || '暂无简介',
      filePath: selectedVersionFile ? selectedVersionFile.path : '',
      fileSize: selectedVersionFile ? formatFileSize(selectedVersionFile.file_size || 0) : '未知大小',
      cast: castMembers
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mediaDetails, mediaFiles, castMembers, selectedVersionFile]);

  // 渲染视频描述
  const renderDescription = useMemo(() => {
    const description = videoData.description || '';
    const isLongText = description.length > 73;

    if (!isLongText) {
      // 如果文本不够长，直接显示全部内容，不显示展开/收起按钮
      return (
        <div className={styles.fullDescription}>
          {description}
        </div>
      );
    }

    return showFullDescription ? (
      <div className={styles.fullDescription}>
        {description}
        <span className={styles.toggleButton} onClick={toggleDescription}>收起</span>
      </div>
    ) : (
      <div className={styles.limitedDescription}>
        <span>{description.substring(0, 115)}...</span>
        <span className={styles.toggleButton} onClick={toggleDescription}>更多</span>
      </div>
    );
  }, [showFullDescription, videoData.description, toggleDescription]);

  // 加载中状态
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>加载中...</div>
        <div className={styles.backButton} onClick={handleBack}>
          <LeftOutlined />
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* 背景视频/图片 */}
      <div className={styles.videoBackground}>
        {mediaDetails?.poster && mediaDetails.poster.length > 0 && (
          <img src={mediaDetails.poster[1] || mediaDetails.poster[0]} alt={videoData.title} />
        )}
      </div>

      {/* 返回按钮 - 固定在顶部 */}
      <div className={styles.backButton} onClick={handleBack}>
        <LeftOutlined />
      </div>

      {/* 可滚动的内容区域 */}
      <div className={styles.scrollableContent}>
        {/* 视频标题 */}
        <h1 className={styles.title}>{videoData.title}</h1>

        {/* 视频信息行 */}
        {classes !== '其他' && (
          <div className={styles.infoRow}>
            <span className={styles.rating}>{Number(videoData.rating).toFixed(1)}</span>
            <PreloadImage src={zhixian} style={{ height: '20px', width: '3px' }} />
            <span>{videoData.year}</span>
            <PreloadImage src={zhixian} style={{ height: '20px', width: '3px' }} />
            <span>{videoData.category}</span>
            <PreloadImage src={zhixian} style={{ height: '20px', width: '3px' }} />
            <span>{videoData.region}</span>
            <PreloadImage src={zhixian} style={{ height: '20px', width: '3px' }} />
            <span>{videoData.duration}</span>
            {/* 视频标签 */}
            {classes === '电影' && (
              <div className={styles.infoRow}>
                {videoData.tags.map((tag, index) => (
                  <span key={index} className={styles.tag}>{tag}</span>
                ))}
              </div>)}
          </div>
        )}

        {/* 视频描述 */}
        {classes !== '其他' && (
          <div className={styles.descriptionContainer}>
            {renderDescription}
          </div>
        )}

        {/* 版本选择器 - 只在电影类型且有多个版本时显示 */}
        {classes === '电影' && availableVersions.length >= 2 && (
          <div className={styles.versionSelector}>
            {/* <span className={styles.versionLabel}>选择版本：</span> */}
            <Select
              value={selectedVersionId}
              onChange={handleVersionSelect}
              className={styles.versionSelect}
              placeholder="请选择版本"
              style={{ width: 300 }}
            >
              {availableVersions.map((version) => (
                <Select.Option key={version.id} value={version.id}>
                  {version.name}
                </Select.Option>
              ))}
            </Select>
          </div>
        )}



        {/* 操作按钮 */}
        <div className={styles.buttons}>
          <div className={styles.primaryButton} onClick={handlePlay}>
            <PreloadImage src={play_white} style={{ width: "30px", height: "30px" }} alt="play_white" />
            <div>{getPlayButtonText}</div>
          </div>
          <div
            className={styles.secondaryButton}
            onClick={hasDownloaded ? undefined : handleDownload}
            style={{
              cursor: hasDownloaded ? 'not-allowed' : 'pointer',
              opacity: hasDownloaded ? 0.6 : 1
            }}
          >
            <DownloadOutlined style={{ fontSize: "20px", color: hasDownloaded ? "#999999" : "white" }} />
            <div style={{ color: hasDownloaded ? "#999999" : "white" }}>下载</div>
          </div>
          <div className={styles.secondaryButton} onClick={handleToggleFavorite}>
            {isFavorite ?
              <HeartFilled style={{ fontSize: "20px", color: "#FF4D4F" }} /> :
              <HeartOutlined style={{ fontSize: "20px", color: "white" }} />
            }
            <div>收藏</div>
          </div>
          <div className={styles.secondaryButton} onClick={handleToggleWatched}>
            <CheckOutlined style={{ fontSize: "20px", color: isWatched ? "#1890FF" : "white" }} />
            <div>已观看</div>
          </div>
          <div className={styles.secondaryButton}>
            <Popover
              content={
                <div className={styles.moreMenu}>
                  <div className={styles.moreMenuItem} onClick={handleEditMatch}>
                    修正匹配信息
                  </div>
                  <div className={styles.moreMenuItem} style={{ color: 'red' }} onClick={handleDelete}>
                    删除
                  </div>
                </div>
              }
              arrow={false}
              trigger="click"
              open={morePopoverVisible}
              onOpenChange={setMorePopoverVisible}
              placement="bottomRight"
            >
              <MoreOutlined style={{ fontSize: "20px", color: "white" }} onClick={handleMoreMenuClick} />
              <div>更多</div>
            </Popover>
          </div>
        </div>

        {/* 剧集列表 */}
        {classes === '电视剧' && mediaFiles.length > 0 && (
          <EpisodeList
            episodes={episodes}
            currentEpisodeId={currentEpisodeId}
            onEpisodeSelect={handleEpisodeSelect}
            onToggleFavorite={handleToggleEpisodeFavorite}
            onToggleWatched={handleToggleEpisodeWatched}
          />
        )}

        {/* 需要向上滚动才能看到的内容 */}

        <div className={classes !== '电影' ? styles.extraInfoSection : ''}>
          {/* 演员列表 */}
          {classes !== '其他' && (
            <div className={styles.castSection}>
              <div className={styles.sectionTitle}>演职人员</div>
              <div className={styles.castList}>
                {videoData.cast.map((member) => (
                  <div key={member.id} className={styles.castItem} onClick={() => handleActorClick(member)}>
                    {member.avatar ? (
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className={styles.castAvatar}
                      />
                    ) : (
                      <div className={styles.castAvatar}>{member.name.charAt(0)}</div>
                    )}
                    <div className={styles.castName}>{member.name}</div>
                    <div className={styles.castRole}>{member.role}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 文件信息 */}
          {classes !== '电视剧' && (
            <div className={styles.fileInfo}>
              <div className={styles.fileInfoItem}>文件路径：{CommonUtils.formatFilePath(videoData.filePath)}</div>
              <div className={styles.fileInfoItem}>文件大小：{videoData.fileSize}</div>
            </div>
          )}
        </div>


      </div>

      {/* 下载提示弹窗 */}
      <Modal
        open={downloadModalVisible}
        onCancel={handleCloseDownloadModal}
        className={styles.downloadModal}
        centered
        footer={[
          <button
            key="ok"
            className={styles.downloadModalBtn}
            onClick={handleCloseDownloadModal}
          >
            知道了
          </button>
        ]}
      >
        <p>下载位置</p>
        <p>手机本地路径/手机本地路径</p>
      </Modal>

      {/* 修正匹配信息组件 */}
      <MatchCorrection
        visible={showMatchCorrection}
        onClose={() => {
          setShowMatchCorrection(false);
        }}
        selectList={[{ media_id }]}
        refresh={refreshData}
      />
    </div>
  );
};

export default VideoDetails;