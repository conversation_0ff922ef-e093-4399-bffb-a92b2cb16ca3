import { List, Toast } from "antd-mobile";
import { useState, useEffect } from "react";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { getSupportCameraList, SupportedCameraModel } from "@/api/ipc";
import { PreloadImage } from "@/components/Image";



const formatApiData = (apiData: SupportedCameraModel[]) => {
  // 获取完整的URL前缀
  const origin = window.location.origin;
  const pathname = window.location.pathname;
  const needPath = pathname.split("/").slice(1, 4).join("/");

  return [
    {
      brand: "小米",
      models: apiData.map((camera, index) => ({
        id: index + 1,
        // 拼接完整的图片URL
        icon: `${origin}/${needPath}/${camera.icon}`,
        name: camera.model_name,
        model: camera.model,
      })),
    },
  ];
};

const SupportedCameras = () => {
  const { isDarkMode } = useTheme();
  const [displayCameraList, setDisplayCameraList] = useState<any[]>([]);

  // 页面曝光埋点
  useEffect(() => {
    window.onetrack?.('track', 'ipc_supportCamera_expose');
  }, []);

  useRequest(getSupportCameraList, {
    onSuccess: (res) => {
      if (res && res.code === 0) {
        const formattedData = formatApiData(res.data.camera);
        setDisplayCameraList(formattedData);
      } else if(res && res.code === 1700){
          Toast.show(res?.result);
      } else {
        console.log("接口返回数据为空或失败");
      }
    },
    onError: (error) => {
      console.error("获取支持的摄像机列表失败:", error);
      // 错误时设置为空数组，不再使用默认数据
      setDisplayCameraList([]);
    },
  });

  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>支持的摄像机</div>
      <div className={styles.content}>
        {displayCameraList.length > 0 ? (
          displayCameraList.map((brand: any) => {
            return (
              <div key={brand.brand} className={styles.brandSection}>
                <div className={styles.brandName}>{brand.brand}</div>
                <List className={styles.cameraList}>
                  {brand.models.map((model: any) => {
                    return (
                      <List.Item
                        key={model.id}
                        className={styles.cameraItem}
                        arrow={false}
                      >
                        <div className={styles.itemContent}>
                          <PreloadImage
                            src={model.icon}
                            alt={model.name}
                            className={styles.cameraIcon}
                            style={{ width: 24, height: 24 }}
                          />
                          <span className={styles.cameraName}>{model.name}</span>
                        </div>
                      </List.Item>
                    );
                  })}
                </List>
              </div>
            );
          })
        ) : (
          <div className={styles.emptyState}>
            <div className={styles.emptyText}>暂无内容</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupportedCameras;
