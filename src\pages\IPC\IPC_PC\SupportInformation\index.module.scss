
.cameraModal {
  :global(.ant-modal-content) {
    border-radius: 32px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: var(--desktop-modal-bg-color);
    padding: 20px 24px 0;
  }
  
  :global(.ant-modal-header) {
    border-bottom: none;
    padding: 0 24px;
    background-color: var(--desktop-modal-bg-color) ;

  }
  
  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    color: var(--title-color);
  }
  
  :global(.ant-modal-close) {
    top: 16px;
    left: 20px;
    right: auto;
  }
  
  :global(.ant-modal-body) {
    padding: 16px 24px 24px;
  }
  
  :global(.ant-list-item) {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: none ;
    transition: background-color 0.3s;
    
    // &:hover {
    //   background-color: rgba(0, 0, 0, 0.02);
    // }
  }
  
  :global(.ant-avatar) {
    width: 32px;
    height: 32px;
  }
  
  :global(.ant-list-item-meta) {
    align-items: center !important;
  }
  
  :global(.ant-list-item-meta-title) {
    font-size: 14px;
    color: var(--title-color);
    font-weight: 500;
    margin-bottom: 0;
  }
}

.modalWrapper {
  height: 94%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  :global{
    .ant-list .ant-list-item .ant-list-item-meta .ant-list-item-meta-title{
      margin: 0;
    }
  }
}

.brandSection {
  margin-bottom: 8px;
  padding: 0 5px;
}

.brandTitle {
  font-size: 12px;
  color: #8c93b0;
  line-height: 1.5;
  margin-bottom: 8px;
  padding: 0;
}

.cameraList {
  padding: 0;
}

.cameraItem {
  border-radius: 8px;
}

.cameraName {
  font-size: 14px;
  color: var(--text-color);
}

.cameraIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  overflow: hidden;
}

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 300px;
}

.emptyText {
  font-size: 16px;
  color: var(--text-secondary-color);
  margin-top: 16px;
}
